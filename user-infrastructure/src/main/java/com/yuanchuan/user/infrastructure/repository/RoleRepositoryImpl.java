package com.yuanchuan.user.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.user.domain.model.Permission;
import com.yuanchuan.user.domain.model.Role;
import com.yuanchuan.user.domain.repository.RoleRepository;
import com.yuanchuan.user.infrastructure.mapper.BusinessAccountRoleMapper;
import com.yuanchuan.user.infrastructure.mapper.PermissionMapper;
import com.yuanchuan.user.infrastructure.mapper.RoleMapper;
import com.yuanchuan.user.infrastructure.po.PermissionPO;
import com.yuanchuan.user.infrastructure.po.RolePO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 角色仓储实现类
 */
@Repository
public class RoleRepositoryImpl implements RoleRepository {

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private BusinessAccountRoleMapper businessAccountRoleMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Override
    public Role save(Role role) {
        RolePO po = toPO(role);
        if (po.getId() == null) {
            roleMapper.insert(po);
        } else {
            roleMapper.updateById(po);
        }
        return toEntity(po);
    }

    @Override
    public Optional<Role> findById(Long id) {
        LambdaQueryWrapper<RolePO> query = new LambdaQueryWrapper<>();
        query.eq(RolePO::getId, id)
                .eq(RolePO::getActive, true);
        RolePO po = roleMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public Optional<Role> findByRoleCode(String roleCode) {
        LambdaQueryWrapper<RolePO> query = new LambdaQueryWrapper<>();
        query.eq(RolePO::getRoleCode, roleCode)
                .eq(RolePO::getActive, true);
        RolePO po = roleMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public List<Role> findAll() {
        LambdaQueryWrapper<RolePO> query = new LambdaQueryWrapper<>();
        query.eq(RolePO::getActive, true);
        List<RolePO> poList = roleMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<Role> findByBusinessAccountId(Long businessAccountId) {
        return roleMapper.findByBusinessAccountId(businessAccountId)
                .stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<Role> findByBusinessAccountIdAndOrgId(Long businessAccountId, Long orgId) {
        return roleMapper.findByBusinessAccountIdAndOrgId(businessAccountId, orgId)
                .stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<Role> findByConditions(String roleId, Integer status, String keyword,Long orgId, PageQueryV pageQuery) {

        List<RolePO> roleList = roleMapper.findByConditions(roleId, status, keyword,
                pageQuery.getUsePaging(),orgId, pageQuery.getOffset(), pageQuery.getPageSize());

        if(CollectionUtils.isEmpty(roleList)){
            return null;
        }

        // 转换实体对象为领域对象
        List<Role> roles = roleList.stream()
               .map(this::toEntity)
               .collect(Collectors.toList());

        Map<Long, PermissionPO> permissionMap = permissionMapper.findAll().stream()
                .collect(Collectors.toMap(PermissionPO::getId, Function.identity()));
        // 按角色构造分组描述
        for (Role role : roles) {
            Map<String, List<String>> grouped = new LinkedHashMap<>();

            if(role.getPermissionIds() != null){
                Arrays.stream(role.getPermissionIds().split(","))
                        .map(Long::parseLong)
                        .map(permissionMap::get)
                        .filter(Objects::nonNull)
                        .filter(p -> "BUTTON".equals(p.getType()))
                        .forEach(p -> {
                            PermissionPO parent = permissionMap.get(p.getParentId());
                            if (parent != null && "MENU".equals(parent.getType())) {
                                grouped
                                        .computeIfAbsent(parent.getPermissionName(), k -> new ArrayList<>())
                                        .add(p.getPermissionName());
                            }
                        });

                // 拼接描述
                String desc = grouped.entrySet().stream()
                        .map(e -> e.getKey() + "：" + String.join("、", e.getValue()))
                        .collect(Collectors.joining(";"));

                role.setPermissionGroupDesc(desc);
            }

        }

        return roles;
    }

    @Override
    public Long countByConditions(String roleId, Integer status, String keyword, Long orgId) {
         return roleMapper.findByConditionsCount(roleId, status, keyword, orgId);
    }

    @Override
    public Role queryRoleById(Long id) {
        RolePO po =  roleMapper.queryRoleById(id);

        if(po == null){
            return null;
        }

        // 转换实体对象为领域对象

        Role role =  toEntity(po);

        Map<Long, PermissionPO> permissionMap = permissionMapper.findAll().stream()
                .collect(Collectors.toMap(PermissionPO::getId, Function.identity()));

        Map<String, List<String>> grouped = new LinkedHashMap<>();

        if(role.getPermissionIds() != null){
            Arrays.stream(role.getPermissionIds().split(","))
                    .map(Long::parseLong)
                    .map(permissionMap::get)
                    .filter(Objects::nonNull)
                    .filter(p -> "BUTTON".equals(p.getType()))
                    .forEach(p -> {
                        PermissionPO parent = permissionMap.get(p.getParentId());
                        if (parent != null && "MENU".equals(parent.getType())) {
                            grouped
                                    .computeIfAbsent(parent.getPermissionName(), k -> new ArrayList<>())
                                    .add(p.getPermissionName());
                        }
                    });

            // 拼接描述
            String desc = grouped.entrySet().stream()
                    .map(e -> e.getKey() + "：" + String.join("、", e.getValue()))
                    .collect(Collectors.joining(";"));

            role.setPermissionGroupDesc(desc);
        }

        return role;
    }

    @Override
    public Role queryRoleById(Long id, Long orgId) {
        RolePO po = roleMapper.queryRoleByIdAndOrgId(id, orgId);

        if(po == null){
            return null;
        }

        // 转换实体对象为领域对象
        Role role = toEntity(po);

        Map<Long, PermissionPO> permissionMap = permissionMapper.findAll().stream()
                .collect(Collectors.toMap(PermissionPO::getId, Function.identity()));
        // 按角色构造分组描述
        Map<String, List<String>> grouped = new LinkedHashMap<>();
        if (role.getPermissionIds() != null) {
            Arrays.stream(role.getPermissionIds().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::valueOf)
                    .forEach(permissionId -> {
                        PermissionPO permission = permissionMap.get(permissionId);
                        if (permission != null) {
                            PermissionPO parent = permissionMap.get(permission.getParentId());
                            if (parent != null && "MENU".equals(parent.getType())) {
                                grouped.computeIfAbsent(parent.getPermissionName(), k -> new ArrayList<>())
                                        .add(permission.getPermissionName());
                            }
                        }
                    });
        }

        // 构造描述字符串
        String description = grouped.entrySet().stream()
                .map(entry -> entry.getKey() + "：" + String.join("、", entry.getValue()))
                .collect(Collectors.joining("；"));

        role.setPermissionGroupDesc(description);

        return role;
    }

    @Override
    public List<Role> findByOrgId(Long orgId) {
        LambdaQueryWrapper<RolePO> query = new LambdaQueryWrapper<>();
        query.eq(RolePO::getOrgId, orgId)
                .eq(RolePO::getActive, true);
        List<RolePO> poList = roleMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    /**
     * 将领域对象转换为持久化对象
     */
    private RolePO toPO(Role role) {
        RolePO po = new RolePO();
        BeanUtils.copyProperties(role, po);
        return po;
    }

    /**
     * 将持久化对象转换为领域对象
     */
    private Role toEntity(RolePO po) {
        Role role = new Role();
        BeanUtils.copyProperties(po, role);
        return role;
    }
}
