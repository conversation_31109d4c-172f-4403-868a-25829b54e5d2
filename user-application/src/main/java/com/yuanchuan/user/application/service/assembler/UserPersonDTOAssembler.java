package com.yuanchuan.user.application.service.assembler;

import com.yuanchuan.authentication.api.dto.TokenDTO;
import com.yuanchuan.authentication.api.dto.TokenRequestDTO;
import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.common.context.AuthAccountDeviceApiDTO;
import com.yuanchuan.common.context.UserContext;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.api.dto.*;
import com.yuanchuan.user.api.response.LoginResponse;
import com.yuanchuan.user.api.service.PermissionService;
import com.yuanchuan.user.api.service.RoleService;
import com.yuanchuan.user.domain.model.BusinessAccount;
import com.yuanchuan.user.domain.model.CustomerAccount;
import com.yuanchuan.user.domain.model.UserPerson;
import com.yuanchuan.user.domain.model.UserRegisterDomain;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户DTO装配器
 * 负责在领域对象和DTO之间进行转换
 */
@Slf4j
@Component
public class UserPersonDTOAssembler {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.authentication}")
    private AuthenticationService authenticationService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private RoleService roleService;

    /**
     * 将领域对象转换为用户响应DTO
     *
     * @param userPerson 用户基本信息
     * @param account    用户账号信息
     * @return 用户响应DTO
     */
    public static UserResponse toUserResponseDTO(UserPerson userPerson, CustomerAccount account) {
        if (userPerson == null || account == null) {
            return null;
        }

        UserResponse response = new UserResponse();
        response.setId(account.getId());
        response.setUsername(userPerson.getPhone() != null ? userPerson.getPhone() : userPerson.getEmail());
        response.setEmail(userPerson.getEmail());
        response.setPhone(userPerson.getPhone());
        response.setNickname(account.getNickName());
        response.setAvatar(account.getAvatar());
        response.setUserStatus(account.getUserStatus());
        response.setCreateTime(account.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        response.setUpdateTime(account.getUpdatedAt().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        return response;
    }


    /**
     * 将领域对象转换为用户响应DTO
     *
     * @param userPerson 用户基本信息
     * @param account    商户账号信息
     * @return 用户响应DTO
     */
    public BusinessAccountResponse toBusinessAccountResponseDTo(UserPerson userPerson, BusinessAccount account) {
        if (userPerson == null || account == null) {
            return null;
        }

        BusinessAccountResponse response = new BusinessAccountResponse();
        response.setId(account.getId());
        response.setEmail(userPerson.getEmail());
        response.setPhone(userPerson.getPhone());
        response.setAccountName(account.getAccountName());
        response.setAccountStatus(account.getAccountStatus());
        response.setCreateTime(account.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        response.setUpdateTime(account.getUpdatedAt().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        return response;
    }


    /**
     * 将注册请求DTO转换为用户领域对象
     *
     * @param request 注册请求DTO
     * @return 用户领域对象
     */
    public UserPerson toDmainVrtify(VerifySmsCodeAndLoginRequest request) {
        if (request == null) {
            return null;
        }

        UserPerson userPerson = new UserPerson();
        userPerson.setPhone(request.getPhone());
        userPerson.setEmail(request.getEmail());
        userPerson.setCreatedAt(LocalDateTime.now());
        userPerson.setUpdatedAt(LocalDateTime.now());
        userPerson.setCreatedBy("system");
        userPerson.setUpdatedBy("system");
        userPerson.setActive(true);

        return userPerson;
    }

    /**
     * 将用户响应DTO转换为登录响应DTO
     *
     * @param userResponse 用户响应DTO
     * @return 登录响应DTO
     */
    public LoginResponse toLoginResponseDTO(UserResponse userResponse) {
        if (userResponse == null) {
            return null;
        }

        LoginResponse response = new LoginResponse();
        response.setUserId(userResponse.getId());

        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setUserId(userResponse.getId());
        userInfo.setNickname(userResponse.getNickname());
        userInfo.setPhone(userResponse.getPhone());
        userInfo.setEmail(userResponse.getEmail());
        response.setUserInfo(userInfo);

        return response;
    }

    /**
     * 将注册请求DTO转换为用户领域对象
     *
     * @param request 注册请求DTO
     * @return 用户领域对象
     */
    public UserPerson toUserPersonDomain(UserRegisterDomain request) {
        if (request == null) {
            return null;
        }

        UserPerson userPerson = new UserPerson();
        userPerson.setPhone(request.getPhone());
        userPerson.setEmail(request.getEmail());
        if (request.getSource() != null) {
            userPerson.setSource(request.getSource().getCode());
        }
        userPerson.setCreatedAt(LocalDateTime.now());
        userPerson.setUpdatedAt(LocalDateTime.now());
        userPerson.setCreatedBy("system");
        userPerson.setUpdatedBy("system");
        userPerson.setActive(true);

        return userPerson;
    }

    /**
     * 将注册请求DTO转换为客户账户领域对象
     *
     * @param request         注册请求DTO
     * @param userPersonId    用户ID
     * @param encodedPassword 加密后的密码
     * @return 客户账户领域对象
     */
    public CustomerAccount toCustomerAccountDomain(UserRegisterDomain request, Long userPersonId, String encodedPassword) {
        if (request == null) {
            return null;
        }

        CustomerAccount account = new CustomerAccount();
        account.setUserPersonId(userPersonId);
        account.setNickName(request.getNickname() != null ? request.getNickname() : CustomerAccount.generateDefaultNickname());
        account.setPassword(encodedPassword);
        account.setPasswordUpdatedAt(LocalDateTime.now());
        // 设置注册来源（平台渠道）
        // 暂时使用APP作为默认值，后续可从请求中获取
        account.setRegisterSource("APP");

        // 设置注册类型（注册方式）
        account.setRegisterType(request.getRegistrationType().getName());
        account.setRegisterIp(request.getRegisterIp());
        account.setRegisterDevice(request.getRegisterDevice());
        account.setRegisterTime(LocalDateTime.now());
        account.setUserStatus("ACTIVE");
        account.setCreatedAt(LocalDateTime.now());
        account.setUpdatedAt(LocalDateTime.now());
        account.setCreatedBy("system");
        account.setUpdatedBy("system");
        account.setActive(true);

        return account;
    }

    /**
     * 将用户响应DTO列表转换为登录响应DTO
     *
     * @param userResponse 用户响应DTO
     * @return 登录响应DTO
     */
    public LoginResponse toPhoneExistsResponseDTO(UserResponse userResponse) {
        LoginResponse response = toLoginResponseDTO(userResponse);
        return response;
    }

    /**
     * 将UserPerson转换为UserInfoDTO
     *
     * @param userPerson 用户基本信息
     * @return 用户信息DTO
     */
    public static UserInfoDTO toUserInfoDTO(UserPerson userPerson) {
        if (userPerson == null) {
            return null;
        }

        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setUserId(userPerson.getId());
        userInfo.setPhone(userPerson.getPhone());
        userInfo.setEmail(userPerson.getEmail());

        // 设置昵称，优先使用CustomerAccount的昵称
        if (userPerson.getCustomerAccount() != null) {
            userInfo.setNickname(userPerson.getCustomerAccount().getNickName());
            userInfo.setAvatar(userPerson.getCustomerAccount().getAvatar());
        } else if (userPerson.getBusinessAccount() != null) {
            userInfo.setNickname(userPerson.getBusinessAccount().getAccountName());
            // BusinessAccount 可能没有头像字段，根据实际情况调整
        }

        return userInfo;
    }

    /**
     * 将UserPerson和CustomerAccount转换为UserInfoDTO
     *
     * @param userPerson      用户基本信息
     * @param customerAccount 客户账户信息
     * @return 用户信息DTO
     */
    public static UserInfoDTO toUserInfoDTO(UserPerson userPerson, CustomerAccount customerAccount) {
        if (userPerson == null) {
            return null;
        }

        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setUserId(userPerson.getId());
        userInfo.setPhone(userPerson.getPhone());
        userInfo.setEmail(userPerson.getEmail());

        if (customerAccount != null) {
            userInfo.setNickname(customerAccount.getNickName());
            userInfo.setAvatar(customerAccount.getAvatar());
        }

        return userInfo;
    }

    /**
     * 将UserPerson和BusinessAccount转换为UserInfoDTO
     *
     * @param userPerson      用户基本信息
     * @param businessAccount 商户账户信息
     * @return 用户信息DTO
     */
    public static UserInfoDTO toUserInfoDTO(UserPerson userPerson, BusinessAccount businessAccount) {
        if (userPerson == null) {
            return null;
        }

        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setUserId(userPerson.getId());
        userInfo.setPhone(userPerson.getPhone());
        userInfo.setEmail(userPerson.getEmail());

        if (businessAccount != null) {
            userInfo.setNickname(businessAccount.getAccountName());
            // 如果BusinessAccount有头像字段，可以在这里设置
            // userInfo.setAvatar(businessAccount.getAvatar());
        }

        return userInfo;
    }

    /**
     * 生成token并构建登录响应
     *
     * @param userPerson 登录响应对象
     * @param platform   平台类型
     * @param deviceInfo 设备信息
     * @return 包含token的登录响应
     */
    public LoginResponse buildLoginResponse(UserPerson userPerson, PlatformType platform, AccountDeviceDTO deviceInfo) {
        // 构建响应
        LoginResponse response = new LoginResponse();
        try {
            platform = platform != null ? platform : PlatformType.CUSTOMER;
            String userName = "admin";
            Long id = userPerson.getId();
            Long accountId = null;
            Long orgId = null;

            if (PlatformType.CUSTOMER == platform) {
                CustomerAccount customerAccount = userPerson.getCustomerAccount();
                userName = customerAccount.getNickName();
                accountId = customerAccount.getId();
            } else if (PlatformType.MERCHANT == platform) {
                BusinessAccount businessAccount = userPerson.getBusinessAccount();
                userName = businessAccount.getAccountName();
                accountId = businessAccount.getId();
                orgId = businessAccount.getOrgId();
            } else if (PlatformType.ADMIN == platform) {
                BusinessAccount businessAccount = userPerson.getBusinessAccount();
                id = businessAccount.getUserPersonId();
                accountId = businessAccount.getId();
                userName = businessAccount.getAccountName();
                orgId = businessAccount.getOrgId();
            }

            // 查询用户角色和权限
            List<String> roles = new ArrayList<>();
            List<String> permissions = new ArrayList<>();

            if (accountId != null) {
                try {
                    // 查询用户角色
                    List<RoleDTO> userRoles = roleService.getUserRoles(accountId);
                    roles = userRoles.stream()
                            .map(role -> role.getRoleCode())
                            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

                    // 查询用户权限
                    List<PermissionTreeDTO> userPermissions = permissionService.getUserPermissionTree(accountId);
                    permissions = extractPermissionCodes(userPermissions);
                    response.setUserRoles(userRoles);
                    response.setUserPermissions(userPermissions);
                } catch (Exception e) {
                    // 如果查询角色权限失败，记录日志但不影响登录
                    log.warn("查询用户角色权限失败: {}", e.getMessage());
                }
            }

            // 生成token
            AuthAccountDeviceApiDTO authAccountDeviceApiDTO =  new AuthAccountDeviceApiDTO();
            BeanUtils.copyProperties(deviceInfo, authAccountDeviceApiDTO);
            TokenRequestDTO tokenRequest = TokenRequestDTO.builder()
                    .userId(id)
                    .businessAccountId(accountId)
                    .username(userName)
                    .orgId(orgId)
                    .platform(platform.getCode())
                    .deviceInfo(authAccountDeviceApiDTO)
                    .deviceId(deviceInfo != null ? deviceInfo.getDeviceId() : null)
                    .roles(roles)
                    .permissions(permissions)
                    .build();
            //todo: token生成 保存设备整个对象信息

            // 调用AuthenticationService生成token
            TokenDTO tokenDTO = authenticationService.generateToken(tokenRequest);


            response.setUserId(userPerson.getId());
            response.setBusinessAccountId(accountId);
            TokenInfoDTO tokenInfo = new TokenInfoDTO();
            tokenInfo.setAccessToken(tokenDTO.getAccessToken());
            tokenInfo.setRefreshToken(tokenDTO.getRefreshToken());
            tokenInfo.setExpireTime(tokenDTO.getExpiresAt());
            response.setToken(tokenInfo);
            response.setIsCrossPlatformUser(userPerson.getIsCrossPlatformUser());

            return response;
        } catch (Exception e) {
            throw new RuntimeException("生成token失败: " + e.getMessage(), e);
        }
    }

    /**
     * 递归提取权限编码
     */
    private List<String> extractPermissionCodes(List<PermissionTreeDTO> permissions) {
        List<String> codes = new ArrayList<>();
        if (permissions != null) {
            for (PermissionTreeDTO permission : permissions) {
                if (permission.getPermissionCode() != null) {
                    codes.add(permission.getPermissionCode());
                }
                if (permission.getChildren() != null && !permission.getChildren().isEmpty()) {
                    codes.addAll(extractPermissionCodes(permission.getChildren()));
                }
            }
        }
        return codes;
    }

}
