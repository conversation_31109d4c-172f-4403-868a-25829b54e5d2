# 用户领域文档

## 领域概述

用户领域是整个系统的核心领域之一，负责管理用户身份认证、权限控制、设备管理、内容配置等核心功能。该领域采用DDD（领域驱动设计）架构，按照业务职责划分为多个子域，每个子域都有明确的边界和职责。

## 技术架构

### 分层架构
用户领域遵循DDD分层架构，包含以下层次：

- **API层 (user-api)** - 对外暴露的Dubbo服务接口定义和DTO对象
- **应用层 (user-application)** - 业务流程编排，不包含业务规则
- **领域层 (user-domain)** - 核心业务逻辑，包含领域模型、领域服务、仓储接口
- **基础设施层 (user-infrastructure)** - 技术实现，包含数据库访问、缓存、消息等
- **启动层 (user-bootstrap)** - 应用程序入口和配置管理

### 技术栈
- **框架**：Spring Boot + Dubbo
- **数据库**：MySQL
- **ORM**：MyBatis-Plus
- **缓存**：Redis
- **消息队列**：RocketMQ
- **容器化**：Docker + Kubernetes

## 子域结构

用户领域包含以下6个子域：

### 1. 用户管理子域
**目录**: `用户管理/`
**核心职责**: 用户注册、登录、个人信息管理、账号绑定等
**主要实体**: UserPerson、CustomerAccount、BusinessAccount
**关键服务**: UserService、UserPersonService

### 2. 角色权限管理子域
**目录**: `角色权限管理/`
**核心职责**: 角色管理、权限管理、组织管理、权限验证
**主要实体**: Role、Permission、Organization
**关键服务**: RoleService、PermissionService

## 核心服务接口

### 用户相关服务
- **UserService** - 用户信息管理服务
- **UserPersonService** - 用户认证服务
- **UserDeviceService** - 设备管理服务

### 权限相关服务
- **RoleService** - 角色管理服务
- **PermissionService** - 权限管理服务

### 支撑服务
- **ContentConfigService** - 内容配置服务
- **AuditLogService** - 审计日志服务
- **TaiwanRegionService** - 地址信息服务

## 数据库设计

### 核心表概览
- **用户相关**: user_person, customer_account, business_account
- **权限相关**: role, permission, organization, role_permission


## 领域事件

### 事件驱动架构
用户领域采用事件驱动架构，通过领域事件实现子域间的松耦合通信。

### 主要事件类型
- **用户生命周期事件**: UserRegisteredEvent, UserProfileUpdatedEvent
- **权限管理事件**: RoleCreatedEvent, PermissionUpdatedEvent
- **设备管理事件**: DeviceBoundEvent, DeviceStatusChangedEvent
- **安全事件**: SecurityEventDetectedEvent, SuspiciousOperationEvent

### 事件处理机制
- 使用RocketMQ作为事件总线
- 支持事件的异步处理和重试
- 提供事件的顺序保证和幂等性

## 安全设计

### 认证机制
- 支持多种登录方式：手机号、邮箱、第三方账号
- JWT Token认证
- 设备指纹识别

### 权限控制
- 基于RBAC的权限模型
- 支持细粒度的权限控制（菜单、页面、按钮、API）
- 多租户权限隔离

### 安全策略
- 密码强度验证
- 设备操作频率限制
- 异常行为监控
- 审计日志记录

## 性能优化

### 缓存策略
- 用户信息缓存
- 权限数据缓存
- 热门查询结果缓存

### 数据库优化
- 合理的索引设计
- 读写分离
- 分库分表

### 异步处理
- 审计日志异步记录
- 事件异步处理
- 批量操作优化

## 监控与运维

### 健康检查
- Dubbo QoS健康检查
- 数据库连接监控
- 缓存状态监控

### 日志管理
- 结构化日志输出
- 日志级别控制
- 日志归档策略

### 指标监控
- 业务指标监控
- 性能指标监控
- 错误率监控

## 部署架构

### 容器化部署
- Docker镜像构建
- Kubernetes部署
- 服务发现和负载均衡

### 配置管理
- 环境配置分离
- 配置热更新
- 敏感信息加密

### 扩展性设计
- 水平扩展支持
- 服务拆分策略
- 数据迁移方案

## 开发规范

### 代码规范
- 遵循DDD设计原则
- 统一的代码风格
- 完善的单元测试

### API设计
- RESTful API规范
- 统一的响应格式
- 完整的API文档

### 数据库规范
- 统一的命名规范
- 合理的字段设计
- 完整的约束定义

## 文档结构

每个子域的文档包含以下内容：
- **子域概述** - 子域的职责和边界
- **实体列表** - 聚合根、实体、值对象
- **数据库结构** - 表结构和DDL语句
- **应用服务接口** - Dubbo服务接口描述
- **领域事件** - 发布和订阅的事件
- **领域关系** - 对外依赖和对内提供
- **技术实现** - 技术栈和设计模式
- **特殊设计** - 子域特有的设计考虑

## 快速导航

- [用户管理子域](./用户管理/README.md)
- [角色权限管理子域](./角色权限管理/README.md)