# 用户管理子域

## 子域概述

用户管理子域是用户领域的核心子域，负责管理用户的注册、登录、个人信息管理、账号绑定等功能，为整个系统提供用户身份认证和基础信息服务。

## 核心职责

1. **用户注册与登录** - 支持手机号、邮箱、第三方账号（Google、Apple、LINE）等多种方式的注册和登录
2. **用户信息管理** - 管理用户的基本信息，如昵称、头像、性别、生日、常居地等
3. **账号绑定管理** - 管理用户的手机号、邮箱绑定和换绑功能
4. **密码管理** - 用户密码设置、修改、验证功能
5. **第三方账号管理** - 第三方账号的绑定、解绑功能
6. **跨平台用户管理** - 支持用户在不同平台间的账号关联

## 实体列表

### 聚合根
- **UserPerson（用户）** - 用户聚合根，负责管理用户的生命周期和状态变更
- **CustomerAccount（客户账号）** - 客户账号聚合根，管理C端用户的账号信息
- **BusinessAccount（商户账号）** - 商户账号聚合根，管理B端用户的账号信息

### 实体
- **UserThirdPartyBinding（第三方绑定）** - 用户第三方账号绑定关系实体
- **UserChangeLog（用户变更日志）** - 用户信息变更记录实体
- **AccountDevice（账号设备）** - 账号设备关联实体

### 值对象
- **UserRegisterDomain（用户注册领域对象）** - 用户注册请求值对象
- **PlatformType（平台类型）** - 平台类型枚举值对象
- **RegistrationTypeEnum（注册类型）** - 注册类型枚举值对象
- **RegisterSourceEnum（注册来源）** - 注册来源枚举值对象

## 数据库结构

### 核心表结构

#### 1. 用户表（user_person）
```sql
CREATE TABLE `user_person` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `source` varchar(20) NOT NULL COMMENT '来源平台',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_person_phone_source` (`phone`, `source`, `active`),
  UNIQUE KEY `uk_user_person_email_source` (`email`, `source`, `active`),
  KEY `idx_user_person_phone` (`phone`),
  KEY `idx_user_person_email` (`email`),
  KEY `idx_user_person_source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 2. 客户账号表（customer_account）
```sql
CREATE TABLE `customer_account` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '账号ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `account_name` varchar(50) DEFAULT NULL COMMENT '账号名称',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别：MALE-男，FEMALE-女，OTHER-其他',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `address` varchar(200) DEFAULT NULL COMMENT '常居地址',
  `province_code` varchar(10) DEFAULT NULL COMMENT '省份代码',
  `city_code` varchar(10) DEFAULT NULL COMMENT '城市代码',
  `region_code` varchar(10) DEFAULT NULL COMMENT '区域代码',
  `province_name` varchar(50) DEFAULT NULL COMMENT '省份名称',
  `city_name` varchar(50) DEFAULT NULL COMMENT '城市名称',
  `region_name` varchar(50) DEFAULT NULL COMMENT '区域名称',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `password` varchar(255) DEFAULT NULL COMMENT '密码（加密）',
  `password_salt` varchar(50) DEFAULT NULL COMMENT '密码盐值',
  `first_app_login_at` datetime DEFAULT NULL COMMENT '首次APP登录时间',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `login_count` int NOT NULL DEFAULT '0' COMMENT '登录次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_customer_account_user` (`user_id`, `active`),
  KEY `idx_customer_account_nickname` (`nickname`),
  KEY `idx_customer_account_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户账号表';
```

#### 3. 商户账号表（business_account）
```sql
CREATE TABLE `business_account` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '账号ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `account_name` varchar(50) NOT NULL COMMENT '账号名称',
  `account_type` varchar(20) NOT NULL DEFAULT 'MERCHANT' COMMENT '账号类型：MERCHANT-商户，OPERATION-运营',
  `password` varchar(255) DEFAULT NULL COMMENT '密码（加密）',
  `password_salt` varchar(50) DEFAULT NULL COMMENT '密码盐值',
  `first_app_login_at` datetime DEFAULT NULL COMMENT '首次APP登录时间',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `login_count` int NOT NULL DEFAULT '0' COMMENT '登录次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_account_user` (`user_id`, `active`),
  UNIQUE KEY `uk_business_account_name` (`account_name`, `active`),
  KEY `idx_business_account_type` (`account_type`),
  KEY `idx_business_account_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户账号表';
```

#### 4. 第三方绑定表（user_third_party_binding）
```sql
CREATE TABLE `user_third_party_binding` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `platform_type` varchar(20) NOT NULL COMMENT '平台类型：GOOGLE、APPLE、LINE等',
  `third_party_user_id` varchar(100) NOT NULL COMMENT '第三方用户ID',
  `third_party_email` varchar(100) DEFAULT NULL COMMENT '第三方邮箱',
  `third_party_nickname` varchar(100) DEFAULT NULL COMMENT '第三方昵称',
  `third_party_avatar` varchar(500) DEFAULT NULL COMMENT '第三方头像',
  `bind_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-已解绑，1-已绑定',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_third_party_binding` (`platform_type`, `third_party_user_id`, `active`),
  KEY `idx_third_party_user` (`user_id`),
  KEY `idx_third_party_platform` (`platform_type`),
  KEY `idx_third_party_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='第三方绑定表';
```

#### 5. 用户变更日志表（user_change_logs）
```sql
CREATE TABLE `user_change_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `change_type` varchar(50) NOT NULL COMMENT '变更类型',
  `old_value` json DEFAULT NULL COMMENT '变更前的值（JSON格式）',
  `new_value` json DEFAULT NULL COMMENT '变更后的值（JSON格式）',
  `operator_id` bigint DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  `change_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_change_logs_user` (`user_id`),
  KEY `idx_user_change_logs_type` (`change_type`),
  KEY `idx_user_change_logs_time` (`change_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户变更日志表';
```

## 应用服务接口描述

### Dubbo服务接口

#### 1. UserService（用户信息服务）
- `getUserProfile()` - 获取用户个人信息
- `updateNickname(UserProfileUpdateDTO.NicknameUpdateRequest)` - 更新用户昵称
- `updateAvatar(UserProfileUpdateDTO.AvatarUpdateRequest)` - 更新用户头像
- `updateGender(UserProfileUpdateDTO.GenderUpdateRequest)` - 更新用户性别
- `updateBirthday(UserProfileUpdateDTO.BirthdayUpdateRequest)` - 更新用户生日
- `updateAddress(UserProfileUpdateDTO.AddressUpdateRequest)` - 更新用户常居地
- `verifyPhone(UserAccountUpdateDTO.PhoneVerifyRequest)` - 验证手机号
- `updatePhone(UserAccountUpdateDTO.PhoneUpdateRequest)` - 换绑手机号
- `bindEmail(UserAccountUpdateDTO.EmailBindRequest)` - 绑定邮箱
- `verifyOriginalEmail(UserAccountUpdateDTO.EmailVerifyRequest)` - 验证原邮箱
- `verifyNewEmail(UserAccountUpdateDTO.EmailVerifyRequest)` - 验证新邮箱
- `setPassword(UserAccountUpdateDTO.PasswordSetRequest)` - 设置密码
- `verifyOriginalPassword(UserAccountUpdateDTO.PasswordUpdateRequest)` - 验证原密码
- `getThirdPartyBindingList()` - 获取第三方绑定列表
- `unbindThirdParty(ThirdPartyBindingDTO.UnbindThirdPartyRequest)` - 解绑第三方账号
- `bindThirdParty(ThirdPartyBindingDTO.BindThirdPartyRequest)` - 绑定第三方账号

#### 2. UserPersonService（用户认证服务）
- `login(String type)` - 第三方登录
- `loginCallback(String type, AuthCallbackRequest)` - 第三方登录回调
- `logout()` - 退出登录
- `deletedUser()` - 注销用户
- `verifySmsCodeAndCreateMerchant(VerifySmsCodeAndBindPhoneRequest, String clientIp)` - 验证短信验证码并创建商户账户
- `verifyEmailCodeAndCreateMerchant(VerifyEmailCodeAndLoginRequest, String clientIp)` - 验证邮箱验证码并创建商户账户
- `passwordLogin(PasswordLoginRequest, String clientIp)` - 账号密码登录
- `unifiedLogin(UnifiedLoginRequest)` - 统一登录接口

## 领域事件描述

### 发布的事件

#### 1. 用户生命周期事件
- **UserRegisteredEvent** - 用户已注册
  - 触发时机：新用户注册成功后
  - 事件数据：用户ID、注册方式、注册时间、平台来源
  - 订阅者：设备管理子域、审计日志子域

- **UserProfileUpdatedEvent** - 用户信息已更新
  - 触发时机：用户个人信息更新后
  - 事件数据：用户ID、更新的字段信息、更新时间
  - 订阅者：审计日志子域

- **UserAccountBoundEvent** - 用户账号已绑定
  - 触发时机：用户绑定手机号或邮箱后
  - 事件数据：用户ID、绑定类型、绑定信息、绑定时间
  - 订阅者：审计日志子域

#### 2. 第三方账号事件
- **ThirdPartyAccountBoundEvent** - 第三方账号已绑定
- **ThirdPartyAccountUnboundEvent** - 第三方账号已解绑

#### 3. 密码管理事件
- **PasswordSetEvent** - 密码已设置
- **PasswordChangedEvent** - 密码已修改

### 订阅的事件

#### 1. 认证服务事件
- **LoginSuccessEvent** - 登录成功事件
  - 处理逻辑：更新用户最后登录时间和登录次数

#### 2. 验证服务事件
- **VerificationCodeVerifiedEvent** - 验证码验证成功事件
  - 处理逻辑：根据验证码类型执行相应的业务逻辑

## 领域关系

### 对外依赖

#### 1. 认证服务
- **依赖接口**：AuthenticationService
- **调用场景**：
  - 用户登录认证
  - Token生成和验证
  - 第三方登录处理

#### 2. 验证服务
- **依赖接口**：VerificationService
- **调用场景**：
  - 发送短信验证码
  - 发送邮箱验证码
  - 验证码验证

#### 3. 密码策略服务
- **依赖接口**：PasswordPolicyService
- **调用场景**：
  - 密码强度验证
  - 密码加密和解密

### 对内提供

#### 1. 为角色权限管理子域提供
- **提供接口**：UserPersonDomainService
- **提供功能**：
  - 用户基本信息查询
  - 用户状态验证

#### 2. 为设备管理子域提供
- **提供接口**：UserPersonDomainService
- **提供功能**：
  - 用户身份验证
  - 用户设备关联

#### 3. 为其他业务领域提供
- **提供接口**：UserService、UserPersonService
- **提供功能**：
  - 用户信息查询
  - 用户身份认证
  - 用户状态管理

## 技术实现

### 技术栈
- **框架**：Spring Boot + Dubbo
- **数据库**：MySQL
- **ORM**：MyBatis-Plus
- **缓存**：Redis
- **消息队列**：RocketMQ
- **密码加密**：BCrypt

### 模块结构
- **user-api** - API层，定义Dubbo服务接口和DTO
- **user-application** - 应用层，实现业务用例编排
- **user-domain** - 领域层，包含聚合根、实体、值对象、领域服务
- **user-infrastructure** - 基础设施层，实现数据持久化和外部集成

### 关键设计模式
- **聚合根模式** - 确保业务一致性
- **领域事件模式** - 实现松耦合通信
- **仓储模式** - 抽象数据访问
- **工厂模式** - 处理复杂对象创建

### 特殊设计

#### 1. 跨平台用户管理
- 支持用户在不同平台间的账号关联
- 通过手机号或邮箱识别跨平台用户
- 提供账号合并和分离功能

#### 2. 多种登录方式支持
- 手机号+验证码登录
- 邮箱+验证码登录
- 账号+密码登录
- 第三方账号登录（Google、Apple、LINE）

#### 3. 密码安全策略
- 密码强度验证（8-32位，至少包含2种字符类型）
- 密码加密存储（BCrypt）
- 密码修改历史记录

#### 4. 用户信息变更追踪
- 记录用户信息的所有变更
- JSON格式存储变更前后的值
- 支持变更历史查询和审计
