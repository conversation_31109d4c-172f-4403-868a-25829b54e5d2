# 角色权限管理子域

## 子域概述

角色权限管理子域是用户领域的重要子域，负责管理系统的角色定义、权限分配、组织架构等功能，为整个系统提供细粒度的访问控制和权限管理能力。

## 核心职责

1. **角色管理** - 角色的创建、更新、删除、查询，支持角色层级和权限配置
2. **权限管理** - 权限的定义、分配、撤销，支持菜单、页面、按钮、API等多种权限类型
3. **组织管理** - 组织架构的管理，支持多租户场景下的权限隔离
4. **角色权限关联** - 角色与权限的关联关系管理
5. **用户角色分配** - 用户与角色的关联关系管理
6. **权限验证** - 提供权限验证服务，支持动态权限检查
7. **角色层级管理** - 支持角色创建权限的层级控制

## 实体列表

### 聚合根
- **Role（角色）** - 角色聚合根，负责管理角色的生命周期和权限分配
- **Permission（权限）** - 权限聚合根，负责管理权限的定义和层级结构
- **Organization（组织）** - 组织聚合根，负责管理组织架构和权限隔离

### 实体
- **RolePermission（角色权限关联）** - 角色与权限的关联关系实体
- **BusinessAccountRole（用户角色关联）** - 商户账号与角色的关联关系实体
- **RoleCreateScope（角色创建权限）** - 角色创建权限配置实体

### 值对象
- **PermissionType（权限类型）** - 权限类型枚举值对象（MENU、PAGE、BUTTON、API）
- **RoleStatus（角色状态）** - 角色状态枚举值对象
- **GrantType（授权类型）** - 授权类型枚举值对象（GRANT、DENY）

## 数据库结构

### 核心表结构

#### 1. 角色表（role）
```sql
CREATE TABLE `role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码，唯一，例如：ADMIN、SHOP_MANAGER',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `org_id` bigint NOT NULL COMMENT '所属组织ID',
  `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用 1-启用',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code_org` (`role_code`, `org_id`, `active`),
  KEY `idx_role_org` (`org_id`),
  KEY `idx_role_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
```

#### 2. 权限表（permission）
```sql
CREATE TABLE `permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `permission_code` varchar(100) NOT NULL COMMENT '权限唯一编码，支持两种格式：1.模块:动作(如USER:READ) 2.功能编码(如MENU_DASHBOARD)',
  `permission_name` varchar(100) NOT NULL COMMENT '权限名称',
  `type` varchar(20) NOT NULL COMMENT '权限类型：MENU-菜单, PAGE-页面, BUTTON-按钮, API-接口',
  `parent_id` bigint DEFAULT NULL COMMENT '父权限ID，构成树形结构',
  `url` varchar(200) DEFAULT NULL COMMENT '页面路径或API路径，菜单和页面用',
  `http_method` varchar(10) DEFAULT NULL COMMENT 'HTTP方法：GET、POST、PUT、DELETE等',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标（菜单类型时）',
  `component_path` varchar(200) DEFAULT NULL COMMENT '组件路径（页面类型时）',
  `order_num` int DEFAULT '0' COMMENT '排序字段',
  `is_visible` tinyint(1) DEFAULT '1' COMMENT '是否可见：0-隐藏，1-显示',
  `description` varchar(200) DEFAULT NULL COMMENT '权限描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用 1-启用',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`, `active`),
  KEY `idx_permission_parent` (`parent_id`),
  KEY `idx_permission_type` (`type`),
  KEY `idx_permission_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表，支持菜单、页面、按钮、API权限';
```

#### 3. 组织表（organization）
```sql
CREATE TABLE `organization` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '组织ID',
  `org_name` varchar(100) NOT NULL COMMENT '组织名称',
  `org_code` varchar(50) NOT NULL COMMENT '组织编码',
  `org_type` varchar(20) NOT NULL COMMENT '组织类型：PLATFORM-平台，MERCHANT-商户',
  `parent_id` bigint DEFAULT NULL COMMENT '父组织ID',
  `description` varchar(200) DEFAULT NULL COMMENT '组织描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_org_code` (`org_code`, `active`),
  KEY `idx_org_parent` (`parent_id`),
  KEY `idx_org_type` (`org_type`),
  KEY `idx_org_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组织表';
```

#### 4. 角色权限关联表（role_permission）
```sql
CREATE TABLE `role_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  `grant_type` varchar(10) NOT NULL DEFAULT 'GRANT' COMMENT '授权类型：GRANT-授权，DENY-拒绝',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`, `active`),
  KEY `idx_role_permission_role` (`role_id`),
  KEY `idx_role_permission_permission` (`permission_id`),
  KEY `idx_role_permission_grant` (`grant_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';
```

#### 5. 用户角色关联表（business_account_role）
```sql
CREATE TABLE `business_account_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `business_account_id` bigint NOT NULL COMMENT '商户账号ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `org_id` bigint NOT NULL COMMENT '组织ID',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_role_org` (`business_account_id`, `role_id`, `org_id`, `active`),
  KEY `idx_account_role_account` (`business_account_id`),
  KEY `idx_account_role_role` (`role_id`),
  KEY `idx_account_role_org` (`org_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户账号角色关联表';
```

#### 6. 角色创建权限表（role_create_scope）
```sql
CREATE TABLE `role_create_scope` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `from_role_id` bigint NOT NULL COMMENT '源角色ID（有权限创建的角色）',
  `to_role_id` bigint NOT NULL COMMENT '目标角色ID（可以被创建的角色）',
  `org_id` bigint NOT NULL COMMENT '组织ID',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_create_scope` (`from_role_id`, `to_role_id`, `org_id`, `active`),
  KEY `idx_role_create_from` (`from_role_id`),
  KEY `idx_role_create_to` (`to_role_id`),
  KEY `idx_role_create_org` (`org_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色创建权限表';
```

## 应用服务接口描述

### Dubbo服务接口

#### 1. RoleService（角色管理服务）
- `queryRoles(RoleQueryRequest)` - 分页查询角色列表
- `getRoleSelectList()` - 获取角色下拉列表
- `createRole(RoleCreateRequest)` - 创建角色
- `updateRole(RoleUpdateRequest)` - 更新角色
- `getRoleById(Long id)` - 根据ID查询角色详情
- `deleteRole(Long id)` - 删除角色
- `getUserRoles(Long accountId)` - 根据账户ID查询用户角色列表
- `getAllowedTargetRoles()` - 获取当前用户允许创建的目标角色列表
- `merchantCreateRole(MerchantCreateRoleRequest)` - 商户创建角色

#### 2. PermissionService（权限管理服务）
- `getUserPermissionTree(Long accountId)` - 根据账户ID查询用户权限树
- `queryPermissions(PermissionQueryRequest)` - 分页查询权限列表
- `getPermissionEntries()` - 获取权限入口下拉列表（类型为MENU的权限）
- `createPermission(PermissionCreateRequest)` - 创建权限
- `updatePermission(PermissionUpdateRequest)` - 更新权限
- `getPermissionById(Long id)` - 根据ID查询权限详情
- `deletePermission(Long id)` - 删除权限

## 领域事件描述

### 发布的事件

#### 1. 角色管理事件
- **RoleCreatedEvent** - 角色已创建
  - 触发时机：新角色创建成功后
  - 事件数据：角色ID、角色名称、组织ID、创建时间、创建人
  - 订阅者：审计日志子域

- **RoleUpdatedEvent** - 角色已更新
  - 触发时机：角色信息更新后
  - 事件数据：角色ID、更新的字段信息、更新时间、更新人
  - 订阅者：审计日志子域

- **RoleDeletedEvent** - 角色已删除
  - 触发时机：角色删除后
  - 事件数据：角色ID、角色名称、删除时间、删除人
  - 订阅者：审计日志子域

#### 2. 权限管理事件
- **PermissionCreatedEvent** - 权限已创建
- **PermissionUpdatedEvent** - 权限已更新
- **PermissionDeletedEvent** - 权限已删除

#### 3. 角色权限关联事件
- **RolePermissionGrantedEvent** - 角色权限已授权
- **RolePermissionRevokedEvent** - 角色权限已撤销

#### 4. 用户角色分配事件
- **UserRoleAssignedEvent** - 用户角色已分配
- **UserRoleUnassignedEvent** - 用户角色已取消分配

### 订阅的事件

#### 1. 用户管理子域事件
- **UserRegisteredEvent** - 用户已注册
  - 处理逻辑：为新注册的商户用户分配默认角色

#### 2. 组织管理事件
- **OrganizationCreatedEvent** - 组织已创建
  - 处理逻辑：为新组织创建默认角色和权限

## 领域关系

### 对外依赖

#### 1. 用户管理子域
- **依赖接口**：UserPersonDomainService
- **调用场景**：
  - 验证用户身份
  - 获取用户基本信息
  - 用户角色分配验证

#### 2. 审计日志子域
- **依赖接口**：AuditLogDomainService
- **调用场景**：
  - 记录角色权限变更日志
  - 记录用户角色分配日志

### 对内提供

#### 1. 为用户管理子域提供
- **提供接口**：RoleService、PermissionService
- **提供功能**：
  - 用户权限验证
  - 用户角色查询
  - 权限树构建

#### 2. 为其他业务领域提供
- **提供接口**：PermissionService
- **提供功能**：
  - 权限验证服务
  - 用户权限查询
  - 角色权限管理

## 技术实现

### 技术栈
- **框架**：Spring Boot + Dubbo
- **数据库**：MySQL
- **ORM**：MyBatis-Plus
- **缓存**：Redis
- **消息队列**：RocketMQ

### 模块结构
- **user-api** - API层，定义Dubbo服务接口和DTO
- **user-application** - 应用层，实现业务用例编排
- **user-domain** - 领域层，包含聚合根、实体、值对象、领域服务
- **user-infrastructure** - 基础设施层，实现数据持久化和外部集成

### 关键设计模式
- **聚合根模式** - 确保业务一致性
- **领域事件模式** - 实现松耦合通信
- **仓储模式** - 抽象数据访问
- **组合模式** - 构建权限树结构

### 特殊设计

#### 1. 权限类型设计
- **MENU权限**：菜单级别的权限控制
- **PAGE权限**：页面级别的权限控制
- **BUTTON权限**：按钮级别的权限控制
- **API权限**：接口级别的权限控制

#### 2. 角色层级设计
- 支持角色创建权限的层级控制
- 通过RoleCreateScope表配置角色创建权限
- 防止权限提升攻击

#### 3. 多租户权限隔离
- 基于组织ID实现权限隔离
- 支持平台级和商户级权限管理
- 确保不同组织间的权限不会互相影响

#### 4. 权限缓存策略
- 用户权限信息缓存到Redis
- 权限变更时自动刷新缓存
- 支持权限的实时生效

#### 5. 权限验证机制
- 支持基于注解的权限验证
- 支持动态权限检查
- 提供权限验证的统一入口
