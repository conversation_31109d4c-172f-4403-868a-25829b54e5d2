package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.Role;

import java.util.List;
import java.util.Optional;

/**
 * 角色仓储接口
 */
public interface RoleRepository {
    /**
     * 保存角色信息
     *
     * @param role 角色信息
     * @return 保存后的角色信息
     */
    Role save(Role role);

    /**
     * 根据ID查询角色
     *
     * @param id 角色ID
     * @return 角色信息
     */
    Optional<Role> findById(Long id);

    /**
     * 根据角色编码查询角色
     *
     * @param roleCode 角色编码
     * @return 角色信息
     */
    Optional<Role> findByRoleCode(String roleCode);

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    List<Role> findAll();

    /**
     * 根据商户账户ID查询角色列表
     *
     * @param businessAccountId 商户账户ID
     * @return 角色列表
     */
    List<Role> findByBusinessAccountId(Long businessAccountId);

    /**
     * 根据商户账户ID和组织ID查询角色列表
     *
     * @param businessAccountId 商户账户ID
     * @param orgId 组织ID
     * @return 角色列表
     */
    List<Role> findByBusinessAccountIdAndOrgId(Long businessAccountId, Long orgId);

    /**
     * 分页查询角色
     *
     * @param roleId 角色名称
     * @param status 状态
     * @param keyword 关键词
     * @param pageQuery 分页查询参数
     * @return 角色列表
     */
    List<Role> findByConditions(String roleId, Integer status, String keyword,Long orgId, com.yuanchuan.common.domain.query.PageQueryV pageQuery);

    /**
     * 统计角色数量
     *
     * @param roleName 角色名称
     * @param status 状态
     * @param keyword 关键词
     * @param orgId 组织ID
     * @return 角色数量
     */
    Long countByConditions(String roleName, Integer status, String keyword, Long orgId);

    /**
     * 根据ID查询角色
     * @param id
     * @return
     */
    Role queryRoleById(Long id);

    /**
     * 根据ID和组织ID查询角色
     * @param id 角色ID
     * @param orgId 组织ID
     * @return
     */
    Role queryRoleById(Long id, Long orgId);

    /**
     * 根据组织ID查询角色列表
     * @param orgId 组织ID
     * @return 角色列表
     */
    List<Role> findByOrgId(Long orgId);
}
