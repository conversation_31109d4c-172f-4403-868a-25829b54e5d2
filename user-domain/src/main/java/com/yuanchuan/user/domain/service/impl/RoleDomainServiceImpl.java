package com.yuanchuan.user.domain.service.impl;

import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.domain.model.*;
import com.yuanchuan.user.domain.repository.*;
import com.yuanchuan.user.domain.service.RoleDomainService;
import com.yuanchuan.user.domain.service.AuditLogDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.Set;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色领域服务实现类
 */
@Slf4j
@Service
public class RoleDomainServiceImpl implements RoleDomainService {

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private RolePermissionRepository rolePermissionRepository;

    @Autowired
    private AuditLogDomainService auditLogDomainService;

    @Autowired
    private BusinessAccountRoleRepository businessAccountRoleRepository;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private BusinessAccountRepository businessAccountRepository;

    @Autowired
    private RoleCreateScopeRepository roleCreateScopeRepository;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Role createRole(Role role, List<Long> permissionIds, String createBy) {
        return createRole(role, permissionIds, null, createBy);
    }

    /**
     * 创建角色（包含目标角色ID）
     */
    @Transactional(rollbackFor = Exception.class)
    public Role createRole(Role role, List<Long> permissionIds, List<Long> targetRoleIds, String createBy) {
        // 校验角色名称唯一性
        if (!checkRoleNameUnique(role.getRoleName(),null)) {
            throw new BusinessException(UsersErrorCode.ROLE_NAME_EXIST.getCode(), UsersErrorCode.ROLE_NAME_EXIST.getMsg());
        }

        if (!checkRoleCodeUnique(role.getRoleCode(),null)) {
            throw new BusinessException(UsersErrorCode.ROLE_CODE_EXIST.getCode(), UsersErrorCode.ROLE_CODE_EXIST.getMsg());
        }

        // 生成角色编码
        //role.setRoleCode(generateRoleCode(role.getRoleName()));

        // 设置默认值
        role.setStatus(1); // 默认启用
        role.setActive(1); // 未删除
        role.setCreatedAt(LocalDateTime.now());
        role.setUpdatedAt(LocalDateTime.now());
        role.setCreatedBy(createBy);
        role.setUpdatedBy(createBy);

        // 保存角色
        Role savedRole = roleRepository.save(role);

        // 保存角色权限关联
        if (!CollectionUtils.isEmpty(permissionIds)) {
            saveRolePermissions(savedRole.getId(), permissionIds,createBy);
        }

        // 保存角色创建权限配置
        if (!CollectionUtils.isEmpty(targetRoleIds)) {
            saveRoleCreateScopes(savedRole.getId(), targetRoleIds, role.getOrgId(), createBy);
        }

        return savedRole;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Role updateRole(Role role, List<Long> permissionIds, Long createId, String createBy) {
        return updateRole(role, permissionIds, null, createId, createBy);
    }

    /**
     * 更新角色（包含目标角色ID）
     */
    @Transactional(rollbackFor = Exception.class)
    public Role updateRole(Role role, List<Long> permissionIds, List<Long> targetRoleIds, Long createId, String createBy) {
        // 校验角色是否存在
        Role existingRole = getRoleById(role.getId());

        // 记录变更前的数据
        Map<String, Object> oldValues = new HashMap<>();
        oldValues.put("role_name", existingRole.getRoleName());
        oldValues.put("description", existingRole.getDescription());
        oldValues.put("status", existingRole.getStatus());

        // 校验角色名称唯一性
        if (!checkRoleNameUnique(role.getRoleName(),role.getId())) {
            throw new BusinessException(UsersErrorCode.ROLE_NAME_EXIST.getCode(), UsersErrorCode.ROLE_NAME_EXIST.getMsg());
        }

        if (!checkRoleCodeUnique(role.getRoleCode(),role.getId())) {
            throw new BusinessException(UsersErrorCode.ROLE_CODE_EXIST.getCode(), UsersErrorCode.ROLE_CODE_EXIST.getMsg());
        }

        // 更新字段
        existingRole.setRoleName(role.getRoleName());
        existingRole.setDescription(role.getDescription());
        if (role.getStatus() != null) {
            existingRole.setStatus(role.getStatus());
        }
        existingRole.setUpdatedAt(LocalDateTime.now());
        existingRole.setUpdatedBy(createBy);

        // 保存角色
        Role savedRole = roleRepository.save(existingRole);

        // 更新角色权限关联
        if (permissionIds != null) {
            // 删除原有关联
            rolePermissionRepository.deleteByRoleId(savedRole.getId());
            // 保存新关联
            if (!CollectionUtils.isEmpty(permissionIds)) {
                saveRolePermissions(savedRole.getId(), permissionIds,createBy);
            }
        }

        // 更新角色创建权限配置
        if (targetRoleIds != null) {
            // 删除原有配置
            roleCreateScopeRepository.deleteByFromRoleId(savedRole.getId(), savedRole.getOrgId());
            // 保存新配置
            if (!CollectionUtils.isEmpty(targetRoleIds)) {
                saveRoleCreateScopes(savedRole.getId(), targetRoleIds, savedRole.getOrgId(), createBy);
            }
        }

        // 记录变更日志
        Map<String, Object> newValues = new HashMap<>();
        newValues.put("role_name", savedRole.getRoleName());
        newValues.put("description", savedRole.getDescription());
        newValues.put("status", savedRole.getStatus());

        // 记录审计日志
        auditLogDomainService.recordAuditLog(
                "ROLE",
                savedRole.getId(),
                "UPDATE",
                createId,
                createBy,
                oldValues,
                newValues,
                "更新角色信息"
        );

        return savedRole;
    }

    @Override
    public Role getRoleById(Long id) {
        Optional<Role> role = roleRepository.findById(id);
        if (!role.isPresent()) {
            throw new BusinessException(UsersErrorCode.ROLE_NOT_EXIST.getCode(), UsersErrorCode.ROLE_NOT_EXIST.getMsg());
        }
        return role.get();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteRole(Long id, Long orgId, String createBy) {
        // 校验角色是否存在
        Role role = getRoleById(id);

        // 验证角色是否属于当前组织
        if (!role.getOrgId().equals(orgId)) {
            throw new BusinessException(UsersErrorCode.ROLE_NOT_EXIST.getCode(), UsersErrorCode.ROLE_NOT_EXIST.getMsg());
        }

        // 校验是否有用户关联此角色，如果有则不允许删除
        List<BusinessAccountRole> accountRoleList =  businessAccountRoleRepository.findByRoleId(role.getId());
        if(!CollectionUtils.isEmpty(accountRoleList)){
            throw new BusinessException(UsersErrorCode.ROLE_RELATED_USER_NOT_ALLOWED_TO_DELETE.getCode(), UsersErrorCode.ROLE_RELATED_USER_NOT_ALLOWED_TO_DELETE.getMsg());
        }
        // 删除角色权限关联
        rolePermissionRepository.deleteByRoleId(id);

        // 逻辑删除角色
        role.setActive(0);
        role.setUpdatedAt(LocalDateTime.now());
        role.setUpdatedBy(createBy);
        roleRepository.save(role);

        return true;
    }

    @Override
    public List<Role> getUserRoles(Long accountId) {
        return roleRepository.findByBusinessAccountId(accountId);
    }

    @Override
    public List<Role> getUserRolesAndOrgId(Long accountId, Long orgId) {
        return roleRepository.findByBusinessAccountIdAndOrgId(accountId,orgId);
    }

    @Override
    public List<Role> findByBusinessAccountId(Long businessAccountId) {
        return roleRepository.findByBusinessAccountId(businessAccountId);
    }

    @Override
    public List<Role> findByBusinessAccountIdAndOrgId(Long businessAccountId,Long orgId) {
        return roleRepository.findByBusinessAccountIdAndOrgId(businessAccountId,orgId);
    }


    @Override
    public List<Role> queryRoles(String roleId, Integer status, String keyword,Long orgId, PageQueryV pageQuery) {
        return roleRepository.findByConditions(roleId, status, keyword,orgId, pageQuery);
    }

    @Override
    public Long countRoles(String roleId, Integer status, String keyword, Long orgId) {
        return roleRepository.countByConditions(roleId, status, keyword, orgId);
    }

    @Override
    public List<Role> getRoleSelectList(Long orgId) {
        return roleRepository.findByOrgId(orgId).stream()
                .filter(role -> role.getStatus() == 1 && role.getActive() == 1)
                .collect(Collectors.toList());
    }

    @Override
    public Boolean checkRoleNameUnique(String roleName, Long excludeId) {
        if (!StringUtils.hasText(roleName)) {
            return true;
        }

        // 查询是否存在同名角色
        List<Role> allRoles = roleRepository.findAll();
        for (Role role : allRoles) {
            if (role.getRoleName().equals(roleName)) {
                // 如果是更新操作且是同一个角色，则允许
                if (excludeId != null && role.getId().equals(excludeId)) {
                    continue;
                }
                return false; // 存在重名角色
            }
        }
        return true;
    }

    @Override
    public Boolean checkRoleCodeUnique(String roleCode, Long excludeId) {
        if (!StringUtils.hasText(roleCode)) {
            return true;
        }

        // 查询是否存在同名角色
        List<Role> allRoles = roleRepository.findAll();
        for (Role role : allRoles) {
            if (role.getRoleCode().equals(roleCode)) {
                // 如果是更新操作且是同一个角色，则允许
                if (excludeId != null && role.getId().equals(excludeId)) {
                    continue;
                }
                return false; // 存在重名角色code
            }
        }
        return true;
    }

    @Override
    public List<Long> getPermissionIdsByRoleId(Long roleId) {
        List<RolePermission> rolePermissions = rolePermissionRepository.findByRoleId(roleId);
        return rolePermissions.stream()
                .map(RolePermission::getPermissionId)
                .collect(Collectors.toList());
    }

    @Override
    public Role queryRoleById(Long id, Long orgId) {
        return roleRepository.queryRoleById(id, orgId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean merchantCreateRole(Long merchantId, String merchantName, String orgType, Long businessAccountId) {
        try {
            // 1. 查询组织是否存在,如果不存在则创建 organization
            Organization organization = findOrCreateOrganization(merchantId, merchantName, orgType);

            // 2. 创建一个这个组织下的admin 角色 role
            Role adminRole = findOrCreateAdminRole(organization, merchantName);

            // 3. 账户运营角色关联 business_account_role
            createBusinessAccountRoleAssociation(businessAccountId, adminRole.getId(),organization.getId());

            // 4. 更新账户信息
            updateBusinessAccountInfo(businessAccountId, organization.getId());
            log.info("商户创建角色成功: merchantId={}, organizationId={}, roleId={}, businessAccountId={}",
                    merchantId, organization.getId(), adminRole.getId(), businessAccountId);

            return true;
        } catch (Exception e) {
            log.error("商户创建角色失败: merchantId={}, error={}", merchantId, e.getMessage(), e);
            throw new BusinessException(UsersErrorCode.SYSTEM_ERROR.getCode(), "商户创建角色失败: " + e.getMessage());
        }
    }



    /**
     * 查找或创建组织
     */
    private Organization findOrCreateOrganization(Long merchantId, String merchantName, String orgType) {
        String orgCode = merchantId.toString();

        // 先查询是否已存在
        Optional<Organization> existingOrg = organizationRepository.findByOrgCode(orgCode);
        if (existingOrg.isPresent()) {
            log.info("组织已存在: orgCode={}", orgCode);
            return existingOrg.get();
        }

        // 不存在则创建
        Organization organization = new Organization();
        organization.setOrgName(merchantName);
        organization.setOrgType(orgType);
        organization.setOrgCode(orgCode);
        organization.setOrgLevel(1); // 顶级组织
        organization.setSortOrder(1);
        organization.setDescription("商户组织: " + merchantName);
        organization.setActive(1);
        organization.setCreatedAt(new Date());
        organization.setUpdatedAt(new Date());
        organization.setCreatedBy("system");
        organization.setUpdatedBy("system");

        Organization savedOrg = organizationRepository.save(organization);
        log.info("创建组织成功: orgId={}, orgCode={}, orgName={}", savedOrg.getId(), orgCode, merchantName);

        return savedOrg;
    }

    /**
     * 查找或创建admin角色
     */
    private Role findOrCreateAdminRole(Organization organization, String merchantName) {
        String roleCode = "ADMIN_MERCHANT" + organization.getOrgCode();

        // 先查询是否已存在该组织的admin角色
        Optional<Role> existingRole = roleRepository.findByRoleCode(roleCode);
        if (existingRole.isPresent()) {
            log.info("Admin角色已存在: roleCode={}", roleCode);
            return existingRole.get();
        }

        // 不存在则创建admin角色
        Role adminRole = new Role();
        adminRole.setRoleCode(roleCode);
        adminRole.setRoleName("超级管理员");
        adminRole.setOrgId(organization.getId());
        adminRole.setDescription("商户管理员角色，拥有所有权限");
        adminRole.setStatus(1); // 启用
        adminRole.setActive(1); // 未删除
        adminRole.setCreatedAt(LocalDateTime.now());
        adminRole.setUpdatedAt(LocalDateTime.now());
        adminRole.setCreatedBy("system");
        adminRole.setUpdatedBy("system");

        Role savedRole = roleRepository.save(adminRole);
        log.info("创建Admin角色成功: roleId={}, roleCode={}", savedRole.getId(), roleCode);

        return savedRole;
    }

    /**
     * 为角色分配所有权限
     */
    private void assignAllPermissionsToRole(Long roleId) {
        try {
            // 获取所有启用的权限
            List<Permission> allPermissions = permissionRepository.findAll();
            if (CollectionUtils.isEmpty(allPermissions)) {
                log.warn("没有找到任何权限，跳过权限分配");
                return;
            }

            // 提取权限ID列表
            List<Long> permissionIds = allPermissions.stream()
                    .filter(permission -> permission.getStatus() != null && permission.getStatus() == 1) // 只分配启用的权限
                    .map(Permission::getId)
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(permissionIds)) {
                saveRolePermissions(roleId, permissionIds, "system");
                log.info("为角色分配权限成功: roleId={}, permissionCount={}", roleId, permissionIds.size());
            }
        } catch (Exception e) {
            log.error("为角色分配权限失败: roleId={}, error={}", roleId, e.getMessage(), e);
            // 权限分配失败不影响角色创建，只记录日志
        }
    }

    /**
     * 创建账户角色关联
     */
    private void createBusinessAccountRoleAssociation(Long businessAccountId, Long roleId,Long organizationId) {
        // 先查询是否已存在关联
        Optional<BusinessAccountRole> existingAssociation =
                businessAccountRoleRepository.findByBusinessAccountIdAndRoleId(businessAccountId, roleId,organizationId);

        if (existingAssociation.isPresent()) {
            log.info("账户角色关联已存在: businessAccountId={}, roleId={}", businessAccountId, roleId);
            return;
        }

        // 不存在则创建关联
        BusinessAccountRole businessAccountRole = new BusinessAccountRole();
        businessAccountRole.setBusinessAccountId(businessAccountId);
        businessAccountRole.setRoleId(roleId);
        businessAccountRole.setOrgId(organizationId);
        businessAccountRole.setCreatedAt(LocalDateTime.now());
        businessAccountRole.setUpdatedAt(LocalDateTime.now());
        businessAccountRole.setCreatedBy("system");
        businessAccountRole.setUpdatedBy("system");
        businessAccountRole.setActive(true);

        BusinessAccountRole savedAssociation = businessAccountRoleRepository.save(businessAccountRole);
        log.info("创建账户角色关联成功: associationId={}, businessAccountId={}, roleId={}",
                savedAssociation.getId(), businessAccountId, roleId);
    }


    /**
     * 生成角色编码
     */
    private String generateRoleCode(String roleName) {
        // 简单实现：使用时间戳 + 随机数
        return "ROLE_" + System.currentTimeMillis();
    }

    /**
     * 保存角色权限关联
     */
    private void saveRolePermissions(Long roleId, List<Long> permissionIds,String createBy) {
        List<RolePermission> rolePermissions = permissionIds.stream()
                .map(permissionId -> {
                    RolePermission rolePermission = new RolePermission();
                    rolePermission.setRoleId(roleId);
                    rolePermission.setPermissionId(permissionId);
                    rolePermission.setGrantType("GRANT");
                    rolePermission.setActive(1);
                    rolePermission.setCreatedAt(new Date());
                    rolePermission.setUpdatedAt(new Date());
                    rolePermission.setCreatedBy(createBy);
                    rolePermission.setUpdatedBy(createBy);
                    return rolePermission;
                })
                .collect(Collectors.toList());

        rolePermissionRepository.batchSave(rolePermissions);
    }

    /**
     * 更新账户信息
     * @param businessAccountId
     * @param orgId
     */
    private void updateBusinessAccountInfo(Long businessAccountId, Long orgId) {
        BusinessAccount businessAccount =  new BusinessAccount();
        businessAccount.setId(businessAccountId);
        businessAccount.setOrgId(orgId);
        businessAccount.setUpdatedAt(LocalDateTime.now());
        businessAccount.setUpdatedBy("system");
        // 更新账户信息
        businessAccountRepository.save(businessAccount);
        log.info("更新账户信息成功: businessAccountId={}, orgId={}", businessAccountId, orgId);
    }

    @Override
    public List<Long> getAllowedTargetRoleIds(List<Long> currentUserRoleIds, Long orgId) {
        if (CollectionUtils.isEmpty(currentUserRoleIds)) {
            return new ArrayList<>();
        }

        Set<Long> allowedRoleIds = new HashSet<>();
        for (Long roleId : currentUserRoleIds) {
            List<Long> targetRoleIds = roleCreateScopeRepository.findTargetRoleIdsByFromRoleId(roleId, orgId);
            allowedRoleIds.addAll(targetRoleIds);
        }

        return new ArrayList<>(allowedRoleIds);
    }

    @Override
    public Boolean validateRoleCreationPermission(List<Long> currentUserRoleIds, List<Long> targetRoleIds, Long orgId) {
        if (CollectionUtils.isEmpty(targetRoleIds)) {
            return true; // 如果没有指定目标角色，则允许
        }

        List<Long> allowedTargetRoleIds = getAllowedTargetRoleIds(currentUserRoleIds, orgId);

        // 检查所有目标角色是否都在允许的范围内
        for (Long targetRoleId : targetRoleIds) {
            if (!allowedTargetRoleIds.contains(targetRoleId)) {
                return false;
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRoleCreateScopes(Long roleId, List<Long> targetRoleIds, Long orgId, String createdBy) {
        if (CollectionUtils.isEmpty(targetRoleIds)) {
            return;
        }

        List<RoleCreateScope> scopes = targetRoleIds.stream()
                .map(targetRoleId -> {
                    RoleCreateScope scope = new RoleCreateScope();
                    scope.setFromRoleId(roleId);
                    scope.setToRoleId(targetRoleId);
                    scope.setOrgId(orgId);
                    scope.setActive(1);
                    scope.setCreatedAt(LocalDateTime.now());
                    scope.setUpdatedAt(LocalDateTime.now());
                    scope.setCreatedBy(createdBy);
                    scope.setUpdatedBy(createdBy);
                    return scope;
                })
                .collect(Collectors.toList());

        roleCreateScopeRepository.saveAll(scopes);
        log.info("保存角色创建权限配置成功: roleId={}, targetRoleIds={}", roleId, targetRoleIds);
    }

    @Override
    public List<Long> getTargetRoleIdsByRoleId(Long roleId, Long orgId) {
        return roleCreateScopeRepository.findTargetRoleIdsByFromRoleId(roleId, orgId);
    }
}
